import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_video_player.dart';

/// A modal dialog for displaying persona introduction videos
///
/// This widget provides:
/// - Full-screen modal presentation
/// - Video player with controls
/// - Persona information display
/// - Proper modal lifecycle management
/// - Responsive design
class PersonaVideoModal extends StatelessWidget {
  final models.SystemPersona persona;
  final String videoPath;

  const PersonaVideoModal({
    super.key,
    required this.persona,
    required this.videoPath,
  });

  /// Show the video modal in fullscreen
  static Future<void> show(
    BuildContext context, {
    required models.SystemPersona persona,
    String videoPath = 'assets/persona-videos/ZenMasterVideo.mp4',
  }) {
    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black,
      barrierLabel: 'Close video modal',
      pageBuilder: (context, animation, secondaryAnimation) =>
          PersonaVideoModal(persona: persona, videoPath: videoPath),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(opacity: animation, child: child);
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Fullscreen video player
            Center(
              child: AspectRatio(
                aspectRatio: 9 / 16, // 9:16 aspect ratio for 1080x1920 videos
                child: _buildVideoSection(context),
              ),
            ),

            // Close button overlay
            Positioned(
              top: AppDimensions.spacingM,
              right: AppDimensions.spacingM,
              child: _buildCloseButton(context),
            ),

            // Persona info overlay (bottom)
            Positioned(
              bottom: AppDimensions.spacingM,
              left: AppDimensions.spacingM,
              right: AppDimensions.spacingM,
              child: _buildPersonaInfo(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoSection(BuildContext context) {
    return PersonaVideoPlayer(
      videoPath: videoPath,
      autoPlay: true,
      looping: false,
      showControls: true,
      aspectRatio: 9 / 16, // 9:16 aspect ratio for vertical videos
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(Icons.close, color: Colors.white, size: 24),
        tooltip: 'Close video',
      ),
    );
  }

  Widget _buildPersonaInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: AppDimensions.borderRadiusM,
      ),
      child: Row(
        children: [
          // Persona avatar
          CircleAvatar(
            radius: 20,
            backgroundColor: context.colorScheme.primaryContainer,
            backgroundImage: _getAvatarImage(),
            child: _getAvatarImage() == null
                ? Icon(
                    AppIcons.profile,
                    size: 20,
                    color: context.colorScheme.onPrimaryContainer,
                  )
                : null,
          ),

          SizedBox(width: AppDimensions.spacingM),

          // Persona name
          Expanded(
            child: Text(
              persona.name,
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  ImageProvider? _getAvatarImage() {
    if (persona.avatarUrl == null || persona.avatarUrl!.isEmpty) {
      return null;
    }

    if (persona.avatarUrl!.startsWith('assets/')) {
      return AssetImage(persona.avatarUrl!);
    } else {
      return NetworkImage(persona.avatarUrl!);
    }
  }
}

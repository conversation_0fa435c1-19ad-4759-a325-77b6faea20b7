import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/models.dart' as models;
import '../services/firestore.dart';
import '../services/analytics_service.dart';
import '../theme/theme.dart';

import '../widgets/persona_video_modal.dart';
import '../widgets/persona_selection_carousel.dart';
import 'chat_page.dart';

class PersonaSelectionPage extends StatefulWidget {
  const PersonaSelectionPage({super.key});

  @override
  State<PersonaSelectionPage> createState() => _PersonaSelectionPageState();
}

class _PersonaSelectionPageState extends State<PersonaSelectionPage> {
  List<models.SystemPersona> _systemPersonas = [];
  List<models.SystemPersona> _orderedPersonas = [];
  String? _selectedPersonaId;
  bool _isLoading = true;
  String? _errorMessage;
  models.User? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Load user data and system personas in parallel
      final results = await Future.wait([
        FirestoreService.getUser(currentUser.uid),
        FirestoreService.getActiveSystemPersonas(),
      ]);

      _currentUser = results[0] as models.User?;
      _systemPersonas = results[1] as List<models.SystemPersona>;

      // Order personas: preferred first, then remaining
      _orderPersonas();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load personas: $e';
        _isLoading = false;
      });
    }
  }

  void _orderPersonas() {
    final preferredIds = _currentUser?.preferredPersonaIds ?? [];
    final preferredPersonas = <models.SystemPersona>[];
    final remainingPersonas = <models.SystemPersona>[];

    for (final persona in _systemPersonas) {
      if (persona.id != null && preferredIds.contains(persona.id)) {
        preferredPersonas.add(persona);
      } else {
        remainingPersonas.add(persona);
      }
    }

    // Sort preferred personas by their order in preferredPersonaIds
    preferredPersonas.sort((a, b) {
      final aIndex = preferredIds.indexOf(a.id!);
      final bIndex = preferredIds.indexOf(b.id!);
      return aIndex.compareTo(bIndex);
    });

    _orderedPersonas = [...preferredPersonas, ...remainingPersonas];
  }

  void _selectPersona(String? personaId) {
    setState(() {
      _selectedPersonaId = personaId;
    });
  }

  Future<void> _startChatWithPersona(models.SystemPersona persona) async {
    if (persona.id == null) return;

    // Set the selected persona and start chat
    _selectPersona(persona.id);
    await _startChatWithPersonaId(persona.id!);
  }

  Future<void> _startChat() async {
    if (_selectedPersonaId == null) return;
    await _startChatWithPersonaId(_selectedPersonaId!);
  }

  Future<void> _startChatWithPersonaId(String personaId) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Find the selected persona for analytics
      final selectedPersona = _systemPersonas.firstWhere(
        (persona) => persona.id == personaId,
      );

      // Track persona selection in analytics
      await AnalyticsService.instance.logPersonaSelected(
        personaId: personaId,
        personaName: selectedPersona.name,
        selectionContext: 'chat_creation',
      );

      // Create new chat with selected persona
      final chatId = await FirestoreService.createChat(
        currentUser.uid,
        systemPersonaId: personaId,
      );

      // Track chat creation in analytics
      await AnalyticsService.instance.logChatCreated(
        personaId: personaId,
        personaName: selectedPersona.name,
      );

      // Fetch the created chat object
      final chat = await FirestoreService.getChat(currentUser.uid, chatId);

      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      // Navigate to ChatPage with the chat object
      if (mounted && chat != null) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => ChatPage(chat: chat)),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load chat data')),
        );
      }
    } catch (e) {
      // Hide loading indicator
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to start chat: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Coach'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: _buildBody(),
      floatingActionButton: _selectedPersonaId != null
          ? Container(
              margin: const EdgeInsets.only(bottom: 16),
              child: FloatingActionButton.extended(
                onPressed: _startChat,
                label: const Text('Start Chat'),
                icon: const Icon(Icons.chat),
              ),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              AppIcons.error,
              size: AppDimensions.iconXxl,
              color: context.colorScheme.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              _errorMessage!,
              style: context.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            ElevatedButton(onPressed: _loadData, child: const Text('Retry')),
          ],
        ),
      );
    }

    if (_orderedPersonas.isEmpty) {
      return const Center(child: Text('No coaches available'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header text
          Text(
            'Choose Your AI Coach',
            style: context.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.colorScheme.primary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Swipe through our collection of AI coaching personalities. Tap on any avatar to watch their introduction video.',
            style: context.textTheme.bodyLarge?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Unified personas carousel
          PersonaSelectionCarousel(
            personas: _orderedPersonas,
            selectedPersonaId: _selectedPersonaId,
            onPersonaSelected: _selectPersona,
            onAvatarTap: _showPersonaVideo,
            onStartChat: _startChatWithPersona,
            showSelectionIndicator: true,
            showStartChatButton: true,
          ),
        ],
      ),
    );
  }

  void _showPersonaVideo(models.SystemPersona persona) {
    PersonaVideoModal.show(
      context,
      persona: persona,
      videoPath: 'assets/persona-videos/ZenMasterVideo.mp4',
    );
  }
}
